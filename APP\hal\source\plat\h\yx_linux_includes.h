﻿/********************************************************************************
**
** 文件名:     yx_linux_includes.h
** 版权所有:   (h) 2024-2030 厦门雅迅智联科技股份有限公司
** 文件描述:   linux系统常用头文件
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2024/08/31 | 叶德焰 |  创建第一版本
*********************************************************************************/
#ifndef _H_YX_LINUX_INCLUDES_
#define _H_YX_LINUX_INCLUDES_


//#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
//#include <stdarg.h>
//#include <time.h>

/*
********************************************************************************
* 平台头文件
********************************************************************************
*/
#include "simcom_os.h"
#include "simcom_api.h"
#include "simcom_common.h"
#include "simcom_system.h"
#include "simcom_tcpip.h"
#include "simcom_uart.h"
#include "simcom_simcard.h"
//#include "simcom_sms.h"
#include "simcom_gpio.h"
//#include "simcom_audio.h"
//#include "simcom_call.h"
#include "simcom_common.h"
#include "simcom_file_system.h"
//#include "simcom_tts_api.h"
//#include "simcom_tts.h"
//#include "simcom_audio.h"
#include "simcom_debug.h"
//#include "simcom_flash.h"
#include "simcom_fota.h"
//#include "simcom_app_download.h"
#include "simcom_ftps.h"
#include "simcom_https.h"
//#include "simcom_htp_client.h"
#include "simcom_ntp_client.h"
#include "simcom_mqtts_client.h"
#include "sc_spi.h"
//#include "simcom_i2c.h"
//#include "simcom_loc.h"
//#include "simcom_wifi.h"
#ifdef FS_SEEK_END
#undef FS_SEEK_END
#endif
//#include "simcom_network.h"

#include "A7680C_V801_GPIO.h"
#include "pl_crypto.h"

#endif
