/********************************************************************************
**
** 文件名:     adp_http.c
** 版权所有:   (c) 2024-2030 厦门雅迅智联科技股份有限公司
** 文件描述:   该模块主要实现ftp/http/https等url相关功能的注册管理
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2025/03/25 | 梅超 |  创建第一版本
*********************************************************************************/
#include "adp_include.h"
#include "shell_reg_http.h"


#if DEBUG_ADP > 0
#define ADP_LOGI(fmt, ...)  PLT_TRACE(fmt,  ##__VA_ARGS__)
#define ADP_LOGD(fmt, ...)  PLT_TRACE("[adp_http] "fmt,  ##__VA_ARGS__)
#define ADP_LOGE(fmt, ...)  PLT_TRACE("[adp_http] ""%d "fmt, __LINE__,  ##__VA_ARGS__)
#define ADP_LOGP(fmt, ...)  PLT_TRACE("[adp_http] "fmt,  ##__VA_ARGS__)
#else

#define ADP_LOGI(fmt, ...)
#define ADP_LOGD(fmt, ...)
#define ADP_LOGE(fmt, ...)  PLT_TRACE("[adp_http] ""%d "fmt, __LINE__,  ##__VA_ARGS__)
#define ADP_LOGP(fmt, ...)  PLT_TRACE("[adp_http] "fmt,  ##__VA_ARGS__)
#endif

/*******************************************************************************
* 定义模块数据结构
*******************************************************************************/
struct HTTP_CLIENT_T {
    BOOLEAN is_initialized;           // 初始化标志
    HTTP_CLI_CONFIG_T cli_config;     // 请求参数
    HTTP_SSL_CONFIG_T ssl_config;     // SSL参数
	HTTP_CALLBACK_T callback;
    BOOLEAN is_download_mode;
    SCFILE* download_fd;
};

typedef struct {
    sMsgQRef http_msgq;
    sMsgQRef client_msgq;
    sTaskRef http_task;
} http_task_t;

/********************************************************************************
* 定义模块配置参数
********************************************************************************/
#define MAX_PACKET_SIZE         (1024 * 4)
#define MAX_MSG_QUEUE_DEPTH     8
#define DEFAULT_TASK_STACK      (1024 * 16)
#define DEFAULT_TASK_PRIORITY   120
#define SAFE_FREE(p) do { if (p) { ADP_FREE(p); (p) = NULL; } } while(0)

static http_task_t *sp_http = NULL;

/*******************************************************************
** 函数名:     get_drive_from_path
** 函数描述:   根据文件路径获取盘符
** 参数:       file_path - 文件路径
** 返回值:     盘符
********************************************************************/
static INT32S get_drive_from_path(const char *file_path) 
{
    if (file_path == NULL) { 
        ADP_LOGD("post file path is invalid.\n"); 
        return -1;
    }

    if (file_path[0] >= 'A' && file_path[0] <= 'Z' && file_path[1] == ':') {
        switch (file_path[0]) {
            case 'C': return 1; // C盘
            case 'D': return 2; // D盘
            default:  return 1;
        }
    }
    return 1; // 默认C盘符
}

/*******************************************************************
** 函数名:     http_get_body
** 函数描述:   获取HTTP响应体
** 参数:       remaining_len - 剩余长度
** 返回值:     无
********************************************************************/
static INT32S http_get_body(INT32U remaining_len)
{
    return sAPI_HttpRead(1, 0, (remaining_len > MAX_PACKET_SIZE) ? MAX_PACKET_SIZE : remaining_len);
}

/*******************************************************************
** 函数名:     http_term
** 函数描述:   终止HTTP，清理资源
** 参数:       client - HTTP客户端句柄
** 返回值:     无
********************************************************************/
static void http_term(HTTP_CLIENT_T *client) 
{
    if (!client) return;
    if (client->is_initialized) {
        sAPI_HttpTerm();
    }
    if (client->download_fd) {
        ADP_FS_fclose(client->download_fd);
        client->download_fd = NULL;
    }
    SAFE_FREE(client);
}

/*******************************************************************
** 函数名:     apply_ssl_config
** 函数描述:   应用SSL配置
** 参数:       ssl_cfg - SSL配置参数
** 返回值:     无
********************************************************************/
static INT32S apply_ssl_config(const HTTP_SSL_CONFIG_T *ssl_cfg) 
{
    char contxtid_str[16] = {0};
    if (!ssl_cfg) {
        ADP_LOGE("ssl config is null.\n"); 
        return -1;
    }
    snprintf(contxtid_str, sizeof(contxtid_str), "%u", ssl_cfg->ssl_contxtid);

    switch (ssl_cfg->verify_level) {
        case HTTP_SSL_NO: 
            break; // 无需SSL配置
        case HTTP_SSL_VERIFY_NONE:
            sAPI_SslSetContextIdMsg("authmode", ssl_cfg->ssl_contxtid, "0");
            break;
        case HTTP_SSL_VERIFY_SERVER:
            if (!ssl_cfg->root_crt_path) {
                ADP_LOGE("root crt path is null.\n"); 
                return -1;
            }
            sAPI_SslSetContextIdMsg("authmode", ssl_cfg->ssl_contxtid, "1");
            sAPI_SslSetContextIdMsg("cacert", ssl_cfg->ssl_contxtid, (char*)ssl_cfg->root_crt_path);
            break;
        case HTTP_SSL_VERIFY_SERVER_CLIENT:
            if (!ssl_cfg->root_crt_path || !ssl_cfg->client_crt_path || !ssl_cfg->client_key_path) {
                ADP_LOGE("crt or key path is null.\n"); 
                return -1;
            }
            sAPI_SslSetContextIdMsg("authmode", ssl_cfg->ssl_contxtid, "2");
            sAPI_SslSetContextIdMsg("cacert", ssl_cfg->ssl_contxtid, (char*)ssl_cfg->root_crt_path);
            sAPI_SslSetContextIdMsg("clientcert", ssl_cfg->ssl_contxtid, (char*)ssl_cfg->client_crt_path);
            sAPI_SslSetContextIdMsg("clientkey", ssl_cfg->ssl_contxtid, (char*)ssl_cfg->client_key_path);
            break;
        default: 
            ADP_LOGE("invalid ssl verify level: %d.\n", ssl_cfg->verify_level); 
            return -1;
    }

    return sAPI_HttpPara("SSLCFG", contxtid_str);
}

/*******************************************************************
** 函数名:     http_request
** 函数描述:   执行http请求
** 参数:       client - HTTP客户端句柄
** 返回值:     无
********************************************************************/
static INT32S http_request(HTTP_CLIENT_T *client) 
{
    char timeout_str[16] = {0};

    if (!client || !client->is_initialized) {
        ADP_LOGE("client is not initialized.\n"); 
        return -1;
    }
    HTTP_CLI_CONFIG_T *cfg = &client->cli_config;

    /* 设置请求参数 */
    if (cfg == NULL || cfg->url == NULL || cfg->url[0] == '\0') {
        ADP_LOGE("invalid request url.\n"); 
        return -1; // URL为空
    }
    sAPI_HttpPara("URL", (char*)cfg->url);

    if (cfg->connect_timeout > 0) {
        snprintf(timeout_str, sizeof(timeout_str), "%u", cfg->connect_timeout);
        sAPI_HttpPara("CONNECTTO", timeout_str);
    }
    if (cfg->response_timeout > 0) {
        snprintf(timeout_str, sizeof(timeout_str), "%u", cfg->response_timeout);
        sAPI_HttpPara("RECVTO", timeout_str);
    }
    if (cfg->headers && cfg->headers_len > 0) 
        sAPI_HttpPara("USERDATA", (char*)cfg->headers);
    if (cfg->postdata && cfg->postdata_len > 0) 
        sAPI_HttpData((char*)cfg->postdata, cfg->postdata_len);
    
    /* 设置SSL参数 */
    if (cfg->request_type != HTTP_CLIENT_REQUEST_NONE && 
        client->ssl_config.verify_level != HTTP_SSL_NO) {
        INT32S ssl_ret = apply_ssl_config(&client->ssl_config);
        if (ssl_ret != 0) {
            ADP_LOGE("apply ssl config failed.\n"); 
            return ssl_ret;
        }
    }

    /* 执行请求 */
    switch (cfg->request_type) {
        case HTTP_CLIENT_REQUEST_GET:
            return sAPI_HttpAction(0); // 发起GET请求
        case HTTP_CLIENT_REQUEST_POST:
            if (cfg->post_file_path) {
                int drive = get_drive_from_path((const char *)cfg->post_file_path); // 获取文件盘符
                return sAPI_HttpPostfile((char *)cfg->post_file_path, drive); // 发起POST文件请求
            } else {
                return sAPI_HttpAction(1); // 发起普通POST请求
            }
        case HTTP_CLIENT_REQUEST_DELETE:
            return sAPI_HttpAction(3); // 发起DELETE请求
        case HTTP_CLIENT_REQUEST_HEAD:
            return sAPI_HttpAction(2); // 发起HEAD请求
        default: 
            ADP_LOGE("invalid request type: %d.\n", cfg->request_type); 
            return -1; // 其他请求类型暂不支持
    }

    return 0;
}

/*******************************************************************
** 函数名:     response_data_analysis
** 函数描述:   处理HTTP响应数据
** 参数:       client - HTTP客户端句柄
**             resp_code - HTTP响应码
**             event - HTTP回调事件
**             pack - 响应数据包
**             pack_len - 数据包长度
**             total_len - 总数据长度
**             recv_len - 已接收数据长度
** 返回值:     无
********************************************************************/
static void response_data_analysis(HTTP_CLIENT_T *client, INT32S resp_code, HTTP_CALLBACK_EVENT_E event, INT8U *pack, INT32U pack_len, INT32U total_len, INT32U recv_len)
{
    if (!client->callback.http_callback) return;
    HTTP_TRANS_PROGRESS_T progress = {
        .resp_code = resp_code,
        .dtotal = total_len,
        .dlen = recv_len
    };
    client->callback.http_callback(event, pack, pack_len, progress);
}

/*******************************************************************
** 函数名:     handle_http_response
** 函数描述:   处理HTTP响应
** 参数:       client - HTTP客户端句柄
** 返回值:     无
********************************************************************/
static void handle_http_response(HTTP_CLIENT_T *client)
{
    INT32U header_len = 0, body_len = 0, total_len = 0, recv_len = 0;
    INT32S resp_code = 0;
    SIM_MSG_T msgQ_data_recv = { SIM_MSG_INIT, 0, -1, NULL };

     /* 初始化下载文件句柄 */
    if (client->is_download_mode && !client->download_fd) {
        INT32U file_size = 0;
        const char *mode = ADP_FS_GetFileSizeByFileName((const char *)client->cli_config.down_file_path, &file_size) ? "a+" : "w+";
        client->download_fd = ADP_FS_fopen((const char *)client->cli_config.down_file_path, mode);
        if (!client->download_fd) {
            ADP_LOGE("Failed to open download file: %s", client->cli_config.down_file_path);
        }
    }

    // 接收初始响应
    msgQ_data_recv = (SIM_MSG_T){ SIM_MSG_INIT, 0, -1, NULL };
    sAPI_MsgQRecv((sMsgQRef)sp_http->http_msgq, &msgQ_data_recv, SC_SUSPEND);
    SChttpApiTrans *http_transdata_action = (SChttpApiTrans *)(msgQ_data_recv.arg3);
    if (http_transdata_action != NULL) {
        ADP_LOGD("method:%d, response-status-code=%ld, body-content-length=%ld\n", \
                http_transdata_action->method, http_transdata_action->status_code, http_transdata_action->action_content_len);
        body_len = http_transdata_action->action_content_len;
        total_len = body_len;
        resp_code = (INT32S)http_transdata_action->status_code;

        SAFE_FREE(http_transdata_action->data);
        SAFE_FREE(http_transdata_action);
    }

    // 处理响应头
    sAPI_HttpHead();
    msgQ_data_recv = (SIM_MSG_T){ SIM_MSG_INIT, 0, -1, NULL };
    sAPI_MsgQRecv((sMsgQRef)sp_http->http_msgq, &msgQ_data_recv, SC_SUSPEND);
    SChttpApiTrans *http_transdata_header = (SChttpApiTrans *)(msgQ_data_recv.arg3);
    if (http_transdata_header != NULL) {
        header_len = http_transdata_header->dataLen;
        response_data_analysis(client, resp_code, HTTP_CB_EVENT_RSP_HEADER, 
                                (INT8U *)http_transdata_header->data, header_len, header_len, header_len);
        
        SAFE_FREE(http_transdata_header->data);
        SAFE_FREE(http_transdata_header);
    }

    /* 分块处理响应体 */
    while (body_len > 0) {
        //http_get_body(body_len);
        sAPI_HttpRead(1, 0, (remaining_len > MAX_PACKET_SIZE) ? MAX_PACKET_SIZE : remaining_len);
        msgQ_data_recv = (SIM_MSG_T){ SIM_MSG_INIT, 0, -1, NULL };
        sAPI_MsgQRecv((sMsgQRef)sp_http->http_msgq, &msgQ_data_recv, SC_SUSPEND);
        SChttpApiTrans *http_transdata_body = (SChttpApiTrans *)(msgQ_data_recv.arg3);
        if (!http_transdata_body) continue;
        const INT32U current_len = http_transdata_body->dataLen;
        if (current_len > 0) {
            recv_len += current_len;
            body_len -= current_len;
            /* 下载模式处理 */
            if (client->is_download_mode && client->download_fd) {
                const INT32U written = ADP_FS_fwrite(http_transdata_body->data, current_len, client->download_fd);
                if (written != current_len) {
                    ADP_LOGE("Write failed: %u/%u bytes", written, current_len);
                }
            }
            const HTTP_CALLBACK_EVENT_E event = (recv_len == total_len) ? 
                    (client->is_download_mode ? HTTP_CB_EVENT_DOWNLOAD_END : HTTP_CB_EVENT_RSP_END) :
                    (client->is_download_mode ? HTTP_CB_EVENT_DOWNLOAD_PROGRESS : HTTP_CB_EVENT_RSP_CONTENT);
            
            if (event == HTTP_CB_EVENT_DOWNLOAD_END && client->download_fd) {
                // 下载完成，关闭文件句柄再回调
                ADP_LOGD("Download_fd = %p\n", client->download_fd);
                ADP_FS_fclose(client->download_fd);
                client->download_fd = NULL;
                ADP_LOGD("Download completed: %s", client->cli_config.down_file_path);
            } else if (event == HTTP_CB_EVENT_RSP_END) {
                // 响应结束
                ADP_LOGD("Response completed, total length: %u bytes", total_len);
            }
            
            response_data_analysis(client, resp_code, event, 
                                (INT8U *)http_transdata_body->data, current_len, total_len, recv_len);
        }
        SAFE_FREE(http_transdata_body->data);
        SAFE_FREE(http_transdata_body);
    }
}

/*******************************************************************
** 函数名:     http_task
** 函数描述:   http主任务
** 参数:       无
** 返回:       无
********************************************************************/
static void http_task(void *param)
{
    while (1)
    {
        SIM_MSG_T msgQ_data_recv = {SIM_MSG_INIT, 0, -1, NULL};
        // 阻塞等待HTTP客户端句柄
        ADP_LOGD("Waiting for HTTP client handle...\n");

        if (sAPI_MsgQRecv(sp_http->client_msgq, &msgQ_data_recv, SC_SUSPEND) != SC_SUCCESS) {
            continue;
        }
        HTTP_CLIENT_T *client = (HTTP_CLIENT_T *)msgQ_data_recv.arg3;
        if (client == NULL) {
            ADP_LOGE("Invalid HTTP client handle received.\n");
            continue;
        }

        // 发起GET请求
        if (http_request(client) == SC_SUCCESS) {
            // 处理响应数据
            handle_http_response(client);
        }
        // 清理资源
        http_term(client);
    }
}

/*******************************************************************
** 函数名:     ADP_HTTP_Get
** 函数描述:   HTTP Get处理
** 参数:       无
** 返回:       无
********************************************************************/
BOOLEAN ADP_HTTP_Get(INT8U* url,HTTP_RSP_SAVE_T* savetype, HTTP_SSL_CONF_T* sslcfg, HTTP_CB_RESULT callback)
{
    return FALSE;
}

/*******************************************************************
** 函数名:     ADP_HTTP_Create
** 函数描述:   创建HTTP客户端实例
** 参数:       无
** 返回:       成功返回HTTP客户端句柄，失败返回NULL
********************************************************************/
HTTP_CLIENT_T* ADP_HTTP_Create(void)
{
    if (!sp_http || !sp_http->http_msgq) {
        ADP_LOGE("HTTP module not initialized");
        return NULL;
    }
    HTTP_CLIENT_T *client = (HTTP_CLIENT_T*)ADP_ALLOC(sizeof(HTTP_CLIENT_T));
    if (!client) {
        ADP_LOGE("Memory allocation failed");
        return NULL;
    }
    ADP_MEMSET(client, 0, sizeof(HTTP_CLIENT_T));
    client->download_fd = NULL;
    if (sAPI_HttpInit(1, sp_http->http_msgq) != SC_HTTPS_SUCCESS) {
        ADP_FREE(client);
        return NULL;
    }
    client->is_initialized = TRUE;
    return client;
}

/*******************************************************************
** 函数名:     ADP_HTTP_SetCliCfg
** 函数描述:   设置HTTP客户端的请求参数配置
** 参数:       client - HTTP客户端句柄
               config - 请求参数配置信息
** 返回:       成功返回true，失败返回false
********************************************************************/
BOOLEAN ADP_HTTP_SetCliCfg(HTTP_CLIENT_T *client, const HTTP_CLI_CONFIG_T *config) 
{
    if (!client || !config) {
        ADP_LOGE("Invalid parameters");
        http_term(client);
        return FALSE;
    }
    if (!config->url || strlen((const char *)config->url) == 0 ||
        config->connect_timeout < 0 || config->response_timeout < 0 ||
        config->request_type >= HTTP_CLIENT_REQUEST_MAX) {
        ADP_LOGE("Invalid configuration");
        http_term(client);
        return FALSE;
    }
    ADP_MEMCPY(&client->cli_config, config, sizeof(HTTP_CLI_CONFIG_T));
    client->is_download_mode = (config->down_file_path != NULL);
    return TRUE;
}

/*******************************************************************
** 函数名:     ADP_HTTP_SetSSLCfg
** 函数描述:   设置HTTP客户端的SSL配置
** 参数:       client - HTTP客户端句柄
               config - SSL配置信息
** 返回:       成功返回true，失败返回false
********************************************************************/
BOOLEAN ADP_HTTP_SetSSLCfg(HTTP_CLIENT_T *client, const HTTP_SSL_CONFIG_T *config)
{
    if (!client || !config) {
        ADP_LOGE("Invalid parameters");
        http_term(client);
        return FALSE;
    }
    if (config->ssl_contxtid < 0 || config->ssl_contxtid > 9) {
        ADP_LOGE("Invalid SSL context ID");
        http_term(client);
        return FALSE;
    }
    ADP_MEMCPY(&client->ssl_config, config, sizeof(HTTP_SSL_CONFIG_T));
    return TRUE;
}

/*******************************************************************
** 函数名:     ADP_HTTP_Request
** 函数描述:   发起HTTP请求
** 参数:       client - HTTP客户端句柄
               cb - HTTP回调函数
** 返回:       成功返回0，失败返回其他
********************************************************************/
INT32S ADP_HTTP_Request(HTTP_CLIENT_T *client, HTTP_CALLBACK_T cb)
{
    SC_STATUS ret = SC_FAIL;
    if (!client) {
        ADP_LOGE("Invalid client handle");
        return -1;
    }
    if (cb.http_callback) {
        client->callback = cb;
    }
    SIM_MSG_T msg = { SIM_MSG_INIT, 0, -1, client };
    ret = sAPI_MsgQSend(sp_http->client_msgq, &msg);
    if (ret != SC_SUCCESS) {
        ADP_LOGE("Failed to send message\n");
        http_term(client);
        return -1;
    }
    return 0;
}

/*******************************************************************
** 函数名:     ADP_HTTP_Init
** 函数描述:   模块初始化函数
** 参数:       无
** 返回:       无
********************************************************************/
void ADP_HTTP_Init(void)
{
    if (sp_http) {
        ADP_LOGD("HTTP module already initialized");
        return;
    }

    sp_http = (http_task_t *)ADP_ALLOC(sizeof(http_task_t));
    if (sp_http == NULL) {
        ADP_LOGE("Failed to allocate memory for http task\n");
        return;
    }
    ADP_MEMSET(sp_http, 0, sizeof(http_task_t));

    // 创建消息队列
    if (sAPI_MsgQCreate(&sp_http->http_msgq, "http_msgq", sizeof(SIM_MSG_T), MAX_MSG_QUEUE_DEPTH, SC_FIFO) != SC_SUCCESS) {
        ADP_LOGE("http queue create failed\n");
        SAFE_FREE(sp_http);
        return;
    }

    if (sAPI_MsgQCreate(&sp_http->client_msgq, "client_msgq", sizeof(SIM_MSG_T), MAX_MSG_QUEUE_DEPTH, SC_FIFO) != SC_SUCCESS) {
        ADP_LOGE("client queue create failed\n");
        sAPI_MsgQDelete(&sp_http->http_msgq);
        SAFE_FREE(sp_http);
        return;
    }
    // 创建任务线程,单任务,避免内存泄漏
    if (sAPI_TaskCreate(&sp_http->http_task, NULL, DEFAULT_TASK_STACK, DEFAULT_TASK_PRIORITY, "yaxon_http_task", http_task, NULL) != SC_SUCCESS) {
        ADP_LOGE("http task create failed\n");
        sAPI_MsgQDelete(sp_http->http_msgq);
        sAPI_MsgQDelete(sp_http->client_msgq);
        SAFE_FREE(sp_http);
        return;
    }
    ADP_LOGD("http module init success\n");
}

/*******************************************************************
** 函数名:     ADP_MQTT_Deinit
** 函数描述:   模块去初始化函数
** 参数:       无
** 返回:       无
********************************************************************/
void ADP_HTTP_Deinit(void)
{
    if (!sp_http) return;
    
    SIM_MSG_T msg = { SIM_MSG_INIT, 0, -1, NULL };
    sAPI_MsgQSend(sp_http->client_msgq, &msg);
    ADP_RTOS_TaskDelete(&sp_http->http_task);
}