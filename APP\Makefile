# 强制用bash，有的系统/bin/sh默认是用dash的，用dash会导致echo -e显示时多出一个-e
SHELL = /bin/bash

RAR_PATH := $(shell which rar)

VERSION_IS_TEXT := $(shell file version | grep data -o)
VERSION_SDK_IS_TEXT := $(shell file version_sdk | grep data -o)

export BUILD_INFO_FILE_PATH ?= $(shell pwd)/build_info
export BUILD_SDK_TOP = 1

ifeq ($(PARAM_FILE), )
     PARAM_FILE:=./Makefile.param
     include $(PARAM_FILE)
endif

pagesize:=4096
peb-size:=256KiB
leb-size:=253952
max-leb-cnt:=2146

.PHONY:clean all install packet imageall all-keep-go help tar


SUBDIRS :=      \
  comuserv      \
  fk            \
  resman_if     \
  ril_if        \
  monitor       \
  resman        \
  ril           \
  display       \
  hal           \
  gps           \
  update        \
  logcat        \


define show_help_info
	@echo
	@echo "Build Help for $(TARGET)"
	@echo "    make clean           -- clean project"
	@echo "    make all             -- build project, aborted after an error"
	@echo "    make all-keep-go     -- build project, continue as much as possible after an error"
	@echo "    make install         -- install to target"
	@echo "    make packet          -- package image file"
	@echo "    make imageall        -- equal (make clean && make all && make install && make packet)"
	@echo "    make tar             -- one-key archive"
	@echo 
endef

define show_succ_symbol
	@echo
	@echo ".\"\".    .\"\",  "
	@echo "|  |   /  /       "
	@echo "|  |  /  /        "
	@echo "|  | /  /         "
	@echo "|  |/  ;-._       "
	@echo "}  \` _/  / ;     "
	@echo "|  /\` ) /  /     "
	@echo "| /  /_/\_/\      "
	@echo "|/  /      |      "
	@echo "(  ' \ '-  |      "
	@echo " \    \`.  /      "
	@echo "  |      |        "
	@echo "  |      |        "
	@echo
endef

# 获取应用程序版本号
define get_app_ver
	$(1) := $(shell cat version)
endef

# 获取应用程序SDK版本号
define get_app_ver_sdk
	$(1) := $(shell cat version_sdk)
endef

# 获取内核版本号
define get_ver_kernel
	$(1) := $(shell \
	modprobe nandsim first_id_byte=0xec second_id_byte=0xd5 third_id_byte=0x51 fourth_id_byte=0xa6; \
	modprobe ubi; \
	ubiformat /dev/mtd0 -s 4096 -f ../osdrv/image/mdm9607-perf-sysfs.ubi &>/dev/null; \
	ubiattach /dev/ubi_ctrl -m 0 -O 4096 &>/dev/null; \
	mkdir -p ./build/fs/; \
	mount -t ubifs ubi0 ./build/fs/; \
	ver_kernel=`awk 'BEGIN{FS="-"} {print $$NF}' ./build/fs/home/<USER>/version`; \
	umount -f ./build/fs/; \
	rm -fr ./build/fs; \
	ubidetach /dev/ubi_ctrl -m 0; \
	modprobe -r ubifs ubi nandsim; \
	echo $$ver_kernel;
	)
endef

# 获取完整版本号
define get_ver_full
	$(eval $(call get_app_ver,    ver_app))
	$(eval $(call get_ver_kernel, ver_kernel))
	$(eval $(call get_app_ver_sdk, ver_app_sdk))
	$(1) := $(strip $(ver_app))-$(strip $(ver_app_sdk))
endef

# 显示版本号
define show_version_number
	$(eval $(call get_ver_full, ver_full))
	@echo
	@echo "*************************************************************"
	@echo "*"
	@echo -e "* full version is:\033[31m \033[1m $(ver_full) \033[0m"
	@echo "*"
	@echo "*************************************************************"
	@echo
endef

# 提示安装rar
define show_install_rar
	@echo
	@echo "*************************************************************"
	@echo "* rar: 未找到命令，您需要安装rarlinux!"
	@echo "* "
	@echo "* 1.下载"
	@echo "* "
	@echo "* 下载地址：http://www.rarsoft.com/download.htm"
	@echo "* "
	@echo "* wget下载：wget http://www.rarsoft.com/rar/rarlinux-5.3.0.tar.gz"
	@echo "* "
	@echo "* 2.安装"
	@echo "* "
	@echo "* tar -zxf rarlinux-5.3.0.tar.gz"
	@echo "* cd rar"
	@echo "* make"
	@echo "* "
	@echo "*************************************************************"
	@echo
endef

#将形参1指定的目录中的文件拷贝到形参2指定的目录下。
#本函数对形参1指定的目录是否存在、目录中的文件是否存在进行验证判断。
define packet_extrafile_to_flash 
	@if test -d $(1); then \
		if [ "`ls -A $(1)/`" != "" ]; then \
			cp -vf $(1)/* $(2); \
		fi \
	fi
endef

define packet_singlefile_to_flash 
	@if test -f $(1); then \
			cp -vf $(1) $(2); \
	fi
endef

# 提示version文件被绿盾加密
define show_version_err
	@echo
	@echo "*************************************************************"
	@echo "* err: $(1)文件不是文本文件，是不是被绿盾加密了!"
	@echo "* "
	@echo "* 使用以下命令进行确认："
	@echo "* "
	@echo "* file $(1)"
	@echo "* "
	@echo "*************************************************************"
	@echo
endef

#warning error等个数显示
define show_build_info
	@echo "*************************************************************"
    @if [ -f "$(BUILD_INFO_FILE_PATH)" ];then \
    cat $(BUILD_INFO_FILE_PATH); \
    echo "*************************************************************"; \
    echo -e "sdk \033[33m warning  \033[0m:`cat $(BUILD_INFO_FILE_PATH) | grep "warning" | wc -l` \
              \033[33m error \033[0m:`cat $(BUILD_INFO_FILE_PATH) | grep "error" | wc -l`"; \
    echo "show details please \"gedit build_info\"";\
    fi
	@echo "*************************************************************"
endef

help:
	$(show_help_info)

# 即使有的子目录编译失败，仍然继续编译
all-keep-go:
	@-reset
	rm -f $(BUILD_INFO_FILE_PATH)
	@for dir in $(SUBDIRS); do \
	    (cd $$dir && $(MAKE) all 2>> $(BUILD_INFO_FILE_PATH)); \
	    echo && echo && echo; \
	done
	$(show_build_info)

all:
	@-reset
	rm -f $(BUILD_INFO_FILE_PATH)
	@for dir in $(SUBDIRS); do \
	    (cd $$dir && $(MAKE) $@  2>> $(BUILD_INFO_FILE_PATH)); \
	    if [ "$$?" != "0" ]; then\
	    	break; \
	    fi;\
	    echo && echo && echo; \
	done	
	$(show_build_info)
	$(show_succ_symbol)

clean:
	@-reset
	@for dir in $(SUBDIRS); do \
	    (cd $$dir && $(MAKE) $@ && rm -fR _build/* _temp); \
	done
	
	@rm -vf build/*.exe

install:
	@-reset	
	@for dir in $(SUBDIRS); do \
	    (cd $$dir && $(MAKE) $@) || exit 1; \
	done
	
	$(show_succ_symbol)

packet:

# 杜绝version被绿盾加密
ifneq ($(VERSION_IS_TEXT),)
	$(call show_version_err, version)
	exit 1
endif
ifneq ($(VERSION_SDK_IS_TEXT),)
	$(call show_version_err, version_sdk)
	exit 1
endif

	@rm -rf build/packet_image/*
	@mkdir -p build/packet_image/ql
	@mkdir ./app_packet -p

# 是否有额外文件要打包进/mnt/flash/app目录的？
	$(call packet_extrafile_to_flash, "./gps/flash_app", "./app_packet")

	@cp -vf ./build/gps_app.exe             ./app_packet/  && chmod a+x ./app_packet/gps_app.exe
	@cp -vf ./build/resserver.exe           ./app_packet/  && chmod a+x ./app_packet/resserver.exe
	@cp -vf ./build/ril_server.exe          ./app_packet/  && chmod a+x ./app_packet/ril_server.exe
	@cp -vf ./build/update.exe              ./app_packet/  && chmod a+x ./app_packet/update.exe
	@cp -vf ./build/logcat                  ./app_packet/  && chmod a+x ./app_packet/logcat
	@cp -vf ./build/monitor.exe             ./app_packet/  && chmod a+x ./app_packet/monitor.exe
	@cp -vf ./build/display.exe             ./app_packet/  && chmod a+x ./app_packet/display.exe
	@cp -vf ./build/lib_3515Acomuserv.so    ./app_packet/  && chmod a+x ./app_packet/lib_3515Acomuserv.so
	@cp -vf ./version                       ./app_packet/
	@cp -vf ./version_sdk                   ./app_packet/
	@fakeroot $(QL_TOOLS_PATH)/quectel_ubi/mkfs.ubifs -r app_packet -o yx_app.ubifs -m $(pagesize) -e $(leb-size) -c $(max-leb-cnt) -F
	@fakeroot $(QL_TOOLS_PATH)/quectel_ubi/ubinize -o build/packet_image/ql/yx_app.ubi -m $(pagesize) -p $(peb-size) -s $(pagesize) build/yx_app.cfg
	@rm -vf ./yx_app.ubifs
	@rm -rf ./app_packet
	
	@cd ./build; chmod +x ./mkpkt; ./mkpkt; cd ..
	@mv -f  ./build/mainapp                 ./build/packet_image/ql/
	@cp -rf ../osdrv/image/*                ./build/packet_image/ql/
	@cp -rf ./build/U升级说明.txt           ./build/packet_image/
	
	$(show_version_number)
	$(show_succ_symbol)

# 一键归档
tar:

	$(show_version_number)

	@while true; do \
		read -p "是否打包GPS进程源码（默认打包）. [Y/n]: " YESNO; \
		if [ -z $$YESNO ]; then tarGPS="1"; break; fi; \
		case "$$YESNO" in \
			y | Y ) tarGPS="1"; break;; \
			n | N ) tarGPS="0"; break;; \
		esac; \
	done; \
	filename=../osdrv/plat/plt-ec20-*.zip;\
	platfilecnt=`ls $$filename 2>/dev/null | wc -l`; \
	if [ $$platfilecnt != "1" ]; then \
	    echo "error:osdrv/plat/目录下不包含平台文件或包含多个";\
	    exit 1; \
	fi; \
	platfile=`ls $$filename`; \
	platver=` ls $$filename | awk -F '[-(]' '{print $$3;}' `; \
	verpath=$(ver_full)-$$platver; \
	mkdir -p "./$$verpath"; \
	mkdir -p ./build/packet_image/_temp; \
	unzip "$$platfile" -d ./build/packet_image/_temp/; \
	cd ./build/packet_image/_temp/; \
	cp -f ../ql/yx_app.ubi ./;\
	filename="../../../$$verpath/"$$verpath"_发货程序.zip"; \
	rm -f "$$filename" && zip  "$$filename" -r ./* ; \
	cd ../../../; \
	rm -rf ./build/packet_image/_temp/; \
	if [ $$tarGPS = "1" ]; then \
		if [ "`ls -A -I.git -I_temp gps`" = "" ] || [ ! -d gps ]; then \
			echo "No gps directory or directory is empty."; exit 1; \
		fi; \
		cd ./gps; \
		filename="../$$verpath/"$$verpath"_gps源码.zip"; \
		rm -f "$$filename" && git archive --format zip -o $$filename HEAD; \
		cd ../; \
	fi; \


	$(show_succ_symbol)


imageall:clean all install packet
