# adp_http.c 详细设计文档

## 1. 模块概述
`adp_http.c`模块实现了基于 HTTP/HTTPS/FTP 的 URL 相关功能的注册与管理，支持 HTTP 客户端的创建、参数配置、SSL 配置、请求发起、响应处理、文件下载等功能。模块采用消息队列和任务线程异步处理 HTTP 请求，适用于嵌入式系统。

## 2. 主要数据结构
- `HTTP_CLIENT_T`：HTTP 客户端实例，包含初始化标志、请求参数、SSL 配置、回调、下载模式及文件句柄。
- `http_task_t`：HTTP 任务管理结构，包含 HTTP 消息队列、客户端消息队列、任务句柄。

## 3. 主要流程说明
### 3.1 初始化流程
- `ADP_HTTP_Init()`：初始化 HTTP 模块，创建消息队列和任务线程。

### 3.2 客户端创建与配置
- `ADP_HTTP_Create()`：分配并初始化 HTTP 客户端实例。
- `ADP_HTTP_SetCliCfg()`：设置 HTTP 请求参数（URL、超时、请求类型、下载路径等）。
- `ADP_HTTP_SetSSLCfg()`：设置 SSL 相关参数。

### 3.3 发起请求
- `ADP_HTTP_Request()`：将客户端请求通过消息队列发送给 HTTP 任务线程。
- `http_task()`：HTTP 主任务线程，阻塞等待客户端请求，收到后调用 `http_request()` 发起 HTTP 请求。

### 3.4 响应处理
- `handle_http_response()`：处理 HTTP 响应，包括响应头、响应体分块接收、文件写入、回调通知等。
- `response_data_analysis()`：对每一块响应数据进行回调处理。

### 3.5 资源释放
- `http_term()`：终止 HTTP 客户端，释放资源。

## 4. 关键函数说明
- `http_request()`：根据请求类型（GET/POST/DELETE/HEAD）发起 HTTP 请求，支持 SSL 配置和文件上传。
- `apply_ssl_config()`：根据 SSL 配置参数设置 SSL 上下文。
- `http_get_body()`：分块读取 HTTP 响应体。
- `handle_http_response()`：完整处理 HTTP 响应，包括头、体、下载、回调。
- `http_task()`：HTTP 主任务循环，处理客户端请求。

## 5. 典型时序图

```mermaid
sequenceDiagram
    participant UserApp as 用户应用
    participant HTTPClient as HTTP_CLIENT_T
    participant HTTPTask as http_task()
    participant HTTPAPI as sAPI_Http*
    participant FS as 文件系统

    UserApp->>HTTPClient: ADP_HTTP_Create()
    UserApp->>HTTPClient: ADP_HTTP_SetCliCfg()
    UserApp->>HTTPClient: ADP_HTTP_SetSSLCfg()
    UserApp->>HTTPClient: ADP_HTTP_Request(cb)
    HTTPClient->>HTTPTask: 发送消息队列(等待)
    HTTPTask->>HTTPAPI: http_request() 发起请求
    HTTPAPI-->>HTTPTask: 返回请求结果
    alt 请求成功
        HTTPTask->>HTTPAPI: 处理响应头/体(分块)
        loop 响应体分块
            HTTPAPI-->>HTTPTask: 响应数据包(等待)
            alt 下载模式
                HTTPTask->>FS: 写入文件
            end
            HTTPTask->>HTTPClient: 回调通知(进度/结束)
        end
    else 请求失败
        HTTPTask->>HTTPClient: 回调通知(失败)
    end
    HTTPTask->>HTTPClient: http_term() 资源释放
```

## 6. 标准消息等待说明
- 所有消息队列接收（如`sAPI_MsgQRecv`）均采用阻塞等待（SC_SUSPEND），保证消息同步。
- HTTP 响应体采用分块接收，每块数据到达后均等待消息队列通知。
- 任务线程主循环持续等待客户端请求消息。

## 7. 主要接口列表
- `void ADP_HTTP_Init(void);`
- `void ADP_HTTP_Deinit(void);`
- `HTTP_CLIENT_T* ADP_HTTP_Create(void);`
- `BOOLEAN ADP_HTTP_SetCliCfg(HTTP_CLIENT_T*, const HTTP_CLI_CONFIG_T*);`
- `BOOLEAN ADP_HTTP_SetSSLCfg(HTTP_CLIENT_T*, const HTTP_SSL_CONFIG_T*);`
- `INT32S ADP_HTTP_Request(HTTP_CLIENT_T*, HTTP_CALLBACK_T);`

## 8. 资源释放与异常处理
- 所有动态分配内存均通过 `SAFE_FREE` 宏安全释放。
- 任务异常、参数错误、配置错误均有日志输出并释放资源。

---
如需更详细的接口参数说明或流程细节，请补充需求。
