ifeq ($(PARAM_FILE), )
	PARAM_FILE:=../Makefile.param
	include $(PARAM_FILE)
endif

ifeq ($(APP_PARAM_FILE), )
	APP_PARAM_FILE:=../Makefile.base
	include $(APP_PARAM_FILE)
endif

#define complier
#ARM_COMPILER = arm-oe-linux-gnueabi-

# config version
PLATFORM=
APPTYPE=HAL
VERSION=

# target
TARGET		:= $(strip $(PLATFORM))$(strip $(APPTYPE))$(strip $(VERSION))

# define build template, app / lib
TEMPLATE	:= lib

# define configure, static / shared if lib
CONFIG		+= static

# default install dir
BINDIR		?= $(APP_PATH)/APP/build/

# external libraries


# defines
DEFINES		+= -DCONFIG_YAXON_PROJECT_ENABLE
ifeq ($(BUILD_OPTION), hal)
DEFINES     += -DCONFIG_YAXON_HALMAIN_ENABLE
endif
ifeq ($(RELEASE_TYPE), debug)
DEFINES		+= -DCONFIG_YAXON_RELEASE_DEBUG
else ifeq ($(RELEASE_TYPE), origin)
DEFINES		+= -DCONFIG_YAXON_RELEASE_ORIGIN
endif
$(info DEFINES: $(DEFINES))


#LIBS += -lc
#LIBS += -lstdc++
#LIBS += -lgcc
#LIBS += -lg
#LIBS += -lnosys
#LIBS += -lm

# compile flags
#CFLAGS		+= -Wall -O2 -Wno-uninitialized -fno-strict-aliasing
#CFLAGS		+= -mapcs -rdynamic  -funwind-tables -g  -Wall -O2 -Wno-uninitialized -fno-strict-aliasing -g  -Wl,-Map,test.map -ldl 
#LDFLAGS    += -L../fk

include ./Makefile.inc
#include $(APP_PATH)/gsmat/ext_make/Makefile.pathgsm
include ../../config/Makefile.template
