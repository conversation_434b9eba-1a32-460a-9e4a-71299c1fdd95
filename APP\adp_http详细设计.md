# adp_http.c 详细设计文档

## 1. 模块概述

### 1.1 功能描述
`adp_http.c`模块是厦门雅迅智联科技股份有限公司开发的HTTP/HTTPS客户端适配层模块，主要实现以下功能：
- HTTP/HTTPS客户端的创建和管理
- 支持GET、POST、DELETE、HEAD等HTTP请求方法
- SSL/TLS安全连接配置和管理
- 文件上传和下载功能
- 异步消息队列处理机制
- 回调函数机制用于状态通知

### 1.2 设计特点
- 基于消息队列的异步处理架构
- 单任务处理模式，避免内存泄漏
- 支持分块数据传输
- 完善的错误处理和资源管理机制
- 适用于嵌入式系统环境

## 2. 主要数据结构

### 2.1 HTTP客户端结构体
```c
struct HTTP_CLIENT_T {
    BOOLEAN is_initialized;           // 初始化标志
    HTTP_CLI_CONFIG_T cli_config;     // 请求参数配置
    HTTP_SSL_CONFIG_T ssl_config;     // SSL参数配置
    HTTP_CALLBACK_T callback;         // 回调函数
    BOOLEAN is_download_mode;         // 下载模式标志
    SCFILE* download_fd;              // 下载文件句柄
};
```

### 2.2 HTTP任务管理结构体
```c
typedef struct {
    sMsgQRef http_msgq;      // HTTP消息队列
    sMsgQRef client_msgq;    // 客户端消息队列
    sTaskRef http_task;      // HTTP任务句柄
} http_task_t;
```

### 2.3 HTTP请求配置结构体
```c
typedef struct {
    const INT8U *url;                       // 请求URL
    const INT8U *headers;                   // 请求头
    INT32U headers_len;                     // 请求头长度
    const INT8U *postdata;                  // POST数据
    INT32U postdata_len;                    // POST数据长度
    const INT8U *down_file_path;            // 下载文件路径
    const INT8U *post_file_path;            // 上传文件路径
    INT32U connect_timeout;                 // 连接超时时间（秒）
    INT32U response_timeout;                // 响应超时时间（秒）
    HTTP_REQUEST_TYPE_T request_type;       // 请求类型
} HTTP_CLI_CONFIG_T;
```

### 2.4 SSL配置结构体
```c
typedef struct {
    HTTP_SSL_VERIFY_LEVEL_E verify_level;   // SSL验证级别
    INT8S ssl_contxtid;                     // SSL上下文ID（0-9）
    const INT8U *root_crt_path;             // CA证书路径
    const INT8U *client_crt_path;           // 客户端证书路径
    const INT8U *client_key_path;           // 客户端私钥路径
    char* root_crt;                         // CA证书字符串
    char* client_crt;                       // 客户端证书字符串
    char* client_key;                       // 客户端私钥字符串
} HTTP_SSL_CONFIG_T;
```

## 3. 枚举类型定义

### 3.1 SSL验证级别
```c
typedef enum {
    HTTP_SSL_NO = 0,                        // HTTP协议
    HTTP_SSL_VERIFY_NONE,                   // HTTPS不验证
    HTTP_SSL_VERIFY_SERVER,                 // HTTPS验证Server
    HTTP_SSL_VERIFY_SERVER_CLIENT,          // HTTPS验证Server和客户端
    HTTP_SSL_VERIFY_MAX
} HTTP_SSL_VERIFY_LEVEL_E;
```

### 3.2 HTTP请求类型
```c
typedef enum {
    HTTP_CLIENT_REQUEST_NONE = 0,
    HTTP_CLIENT_REQUEST_GET,                // GET请求
    HTTP_CLIENT_REQUEST_POST,               // POST请求
    HTTP_CLIENT_REQUEST_PUT,                // PUT请求
    HTTP_CLIENT_REQUEST_DELETE,             // DELETE请求
    HTTP_CLIENT_REQUEST_HEAD,               // HEAD请求
    HTTP_CLIENT_REQUEST_MAX
} HTTP_REQUEST_TYPE_T;
```

### 3.3 HTTP回调事件类型
```c
typedef enum{
    HTTP_CB_EVENT_REQ_START = 1,            // 请求开始成功事件
    HTTP_CB_EVENT_RSP_HEADER,               // 接收到响应头事件
    HTTP_CB_EVENT_RSP_CONTENT,              // 接收到响应体(body)事件
    HTTP_CB_EVENT_RSP_END,                  // 响应结束事件
    HTTP_CB_EVENT_UPLOAD_PROGRESS,          // 上传进度事件
    HTTP_CB_EVENT_UPLOAD_END,               // 上传结束事件
    HTTP_CB_EVENT_DOWNLOAD_PROGRESS,        // 下载进度事件
    HTTP_CB_EVENT_DOWNLOAD_END,             // 下载结束事件
    HTTP_CB_EVENT_ERROR,                    // 请求失败事件
} HTTP_CALLBACK_EVENT_E;
```

## 4. 模块配置参数

### 4.1 关键配置常量
```c
#define MAX_PACKET_SIZE         (1024 * 4)     // 最大数据包大小：4KB
#define MAX_MSG_QUEUE_DEPTH     8               // 消息队列最大深度
#define DEFAULT_TASK_STACK      (1024 * 16)    // 默认任务栈大小：16KB
#define DEFAULT_TASK_PRIORITY   120             // 默认任务优先级
#define SAFE_FREE(p) do { if (p) { ADP_FREE(p); (p) = NULL; } } while(0)
```

### 4.2 全局变量
```c
static http_task_t *sp_http = NULL;  // HTTP任务管理全局实例
```

## 5. 主要功能流程

### 5.1 模块初始化流程
1. **ADP_HTTP_Init()** - 模块初始化
   - 检查模块是否已初始化
   - 分配http_task_t结构体内存
   - 创建HTTP消息队列（http_msgq）
   - 创建客户端消息队列（client_msgq）
   - 创建HTTP任务线程（yaxon_http_task）
   - 设置任务优先级和栈大小

### 5.2 客户端创建与配置流程
1. **ADP_HTTP_Create()** - 创建HTTP客户端
   - 检查HTTP模块是否已初始化
   - 分配HTTP_CLIENT_T结构体内存
   - 调用sAPI_HttpInit()初始化HTTP客户端
   - 设置初始化标志

2. **ADP_HTTP_SetCliCfg()** - 设置客户端配置
   - 验证参数有效性（URL、超时时间、请求类型等）
   - 复制配置信息到客户端结构体
   - 根据down_file_path设置下载模式标志

3. **ADP_HTTP_SetSSLCfg()** - 设置SSL配置
   - 验证SSL上下文ID范围（0-9）
   - 复制SSL配置信息到客户端结构体

### 5.3 HTTP请求处理流程
1. **ADP_HTTP_Request()** - 发起HTTP请求
   - 验证客户端句柄有效性
   - 设置回调函数
   - 将客户端句柄通过消息队列发送给HTTP任务

2. **http_task()** - HTTP主任务循环
   - 阻塞等待客户端消息队列
   - 接收HTTP客户端句柄
   - 调用http_request()发起请求
   - 调用handle_http_response()处理响应
   - 调用http_term()清理资源

### 5.4 HTTP请求执行流程
1. **http_request()** - 执行HTTP请求
   - 设置URL参数
   - 设置连接和响应超时时间
   - 设置请求头和POST数据
   - 应用SSL配置（如需要）
   - 根据请求类型调用相应的sAPI函数：
     - GET: sAPI_HttpAction(0)
     - POST: sAPI_HttpAction(1) 或 sAPI_HttpPostfile()
     - DELETE: sAPI_HttpAction(3)
     - HEAD: sAPI_HttpAction(2)

### 5.5 SSL配置应用流程
1. **apply_ssl_config()** - 应用SSL配置
   - 根据验证级别设置不同的SSL参数：
     - HTTP_SSL_NO: 无需SSL配置
     - HTTP_SSL_VERIFY_NONE: 设置authmode为0
     - HTTP_SSL_VERIFY_SERVER: 设置authmode为1，配置CA证书
     - HTTP_SSL_VERIFY_SERVER_CLIENT: 设置authmode为2，配置CA证书、客户端证书和私钥
   - 调用sAPI_HttpPara("SSLCFG", context_id)应用配置

## 6. HTTP响应处理机制

### 6.1 响应处理主流程
1. **handle_http_response()** - 处理HTTP响应
   - 初始化下载文件句柄（如果是下载模式）
   - 接收初始响应消息，获取状态码和内容长度
   - 处理响应头
   - 分块处理响应体
   - 根据模式进行文件写入或回调通知

### 6.2 响应数据分析
1. **response_data_analysis()** - 响应数据分析
   - 构造HTTP_TRANS_PROGRESS_T进度信息
   - 调用用户注册的回调函数
   - 传递事件类型、数据包、长度和进度信息

### 6.3 分块数据处理
1. **http_get_body()** - 获取响应体数据
   - 计算当前块大小（不超过MAX_PACKET_SIZE）
   - 调用sAPI_HttpRead()读取数据
   - 返回读取结果

## 7. 文件操作支持

### 7.1 文件下载功能
- 支持将HTTP响应内容直接保存到文件
- 自动处理文件打开、写入和关闭
- 支持断点续传（追加模式）
- 提供下载进度回调

### 7.2 文件上传功能
- 支持POST方式上传文件
- 自动获取文件盘符信息
- 调用sAPI_HttpPostfile()执行上传

### 7.3 盘符处理
1. **get_drive_from_path()** - 获取文件路径盘符
   - 解析文件路径中的盘符信息
   - 支持C盘和D盘识别
   - 默认返回C盘（盘符1）

## 8. 资源管理与异常处理

### 8.1 内存管理
- 使用SAFE_FREE宏安全释放内存，避免重复释放
- 所有动态分配的内存都有对应的释放机制
- 异常情况下自动清理已分配的资源

### 8.2 文件句柄管理
- 下载文件句柄在使用完毕后自动关闭
- 异常情况下确保文件句柄正确释放
- 支持文件追加模式和覆盖模式

### 8.3 任务和消息队列管理
- HTTP任务线程持续运行，处理客户端请求
- 消息队列采用FIFO模式，确保请求顺序处理
- 模块去初始化时正确清理任务和消息队列

### 8.4 错误处理机制
- 完善的参数验证机制
- 详细的错误日志输出
- 异常情况下的资源清理和错误回调

## 9. 主要接口说明

### 9.1 模块管理接口
```c
void ADP_HTTP_Init(void);                    // 初始化HTTP模块
void ADP_HTTP_Deinit(void);                  // 去初始化HTTP模块
```

### 9.2 客户端管理接口
```c
HTTP_CLIENT_T* ADP_HTTP_Create(void);        // 创建HTTP客户端实例
BOOLEAN ADP_HTTP_SetCliCfg(HTTP_CLIENT_T *client, const HTTP_CLI_CONFIG_T *config);  // 设置客户端配置
BOOLEAN ADP_HTTP_SetSSLCfg(HTTP_CLIENT_T *client, const HTTP_SSL_CONFIG_T *config);  // 设置SSL配置
INT32S ADP_HTTP_Request(HTTP_CLIENT_T *client, HTTP_CALLBACK_T cb);                  // 发起HTTP请求
```

### 9.3 历史兼容接口
```c
BOOLEAN ADP_HTTP_Get(INT8U* url, HTTP_RSP_SAVE_T* savetype, HTTP_SSL_CONF_T* sslcfg, HTTP_CB_RESULT callback);  // 历史GET接口（未实现）
```

## 10. 工作时序图

### 10.1 HTTP客户端完整工作时序图

```mermaid
sequenceDiagram
    participant UserApp as 用户应用
    participant HTTPModule as HTTP模块
    participant HTTPClient as HTTP_CLIENT_T
    participant HTTPTask as http_task线程
    participant MsgQueue as 消息队列
    participant HTTPAPI as sAPI_Http*
    participant SSLConfig as SSL配置
    participant FileSystem as 文件系统
    participant Callback as 回调函数

    Note over UserApp,Callback: HTTP模块初始化阶段
    UserApp->>HTTPModule: ADP_HTTP_Init()
    HTTPModule->>MsgQueue: 创建http_msgq消息队列
    HTTPModule->>MsgQueue: 创建client_msgq消息队列
    HTTPModule->>HTTPTask: 创建http_task任务线程
    HTTPTask->>MsgQueue: 阻塞等待client_msgq消息(SC_SUSPEND)

    Note over UserApp,Callback: HTTP客户端创建与配置阶段
    UserApp->>HTTPModule: ADP_HTTP_Create()
    HTTPModule->>HTTPClient: 分配HTTP_CLIENT_T内存
    HTTPModule->>HTTPAPI: sAPI_HttpInit(1, http_msgq)
    HTTPAPI-->>HTTPModule: 返回初始化结果
    HTTPModule-->>UserApp: 返回HTTP客户端句柄

    UserApp->>HTTPModule: ADP_HTTP_SetCliCfg(client, config)
    HTTPModule->>HTTPClient: 验证并设置请求配置
    HTTPModule->>HTTPClient: 设置下载模式标志
    HTTPModule-->>UserApp: 返回配置结果

    UserApp->>HTTPModule: ADP_HTTP_SetSSLCfg(client, ssl_config)
    HTTPModule->>HTTPClient: 验证并设置SSL配置
    HTTPModule-->>UserApp: 返回SSL配置结果

    Note over UserApp,Callback: HTTP请求发起阶段
    UserApp->>HTTPModule: ADP_HTTP_Request(client, callback)
    HTTPModule->>HTTPClient: 设置回调函数
    HTTPModule->>MsgQueue: sAPI_MsgQSend(client_msgq, client)
    HTTPTask->>MsgQueue: sAPI_MsgQRecv(client_msgq) 接收到客户端句柄

    Note over UserApp,Callback: HTTP请求处理阶段
    HTTPTask->>HTTPTask: http_request(client) 开始处理
    HTTPTask->>HTTPAPI: sAPI_HttpPara("URL", url)
    HTTPTask->>HTTPAPI: sAPI_HttpPara("CONNECTTO", timeout)
    HTTPTask->>HTTPAPI: sAPI_HttpPara("RECVTO", timeout)
    HTTPTask->>HTTPAPI: sAPI_HttpPara("USERDATA", headers)
    HTTPTask->>HTTPAPI: sAPI_HttpData(postdata, len)

    alt SSL配置需要应用
        HTTPTask->>SSLConfig: apply_ssl_config()
        SSLConfig->>HTTPAPI: sAPI_SslSetContextIdMsg("authmode", id, mode)
        SSLConfig->>HTTPAPI: sAPI_SslSetContextIdMsg("cacert", id, cert)
        SSLConfig->>HTTPAPI: sAPI_SslSetContextIdMsg("clientcert", id, cert)
        SSLConfig->>HTTPAPI: sAPI_SslSetContextIdMsg("clientkey", id, key)
        SSLConfig->>HTTPAPI: sAPI_HttpPara("SSLCFG", context_id)
    end

    alt GET请求
        HTTPTask->>HTTPAPI: sAPI_HttpAction(0)
    else POST请求(数据)
        HTTPTask->>HTTPAPI: sAPI_HttpAction(1)
    else POST请求(文件)
        HTTPTask->>HTTPAPI: sAPI_HttpPostfile(file_path, drive)
    else DELETE请求
        HTTPTask->>HTTPAPI: sAPI_HttpAction(3)
    else HEAD请求
        HTTPTask->>HTTPAPI: sAPI_HttpAction(2)
    end

    HTTPAPI-->>HTTPTask: 返回请求发起结果

    Note over UserApp,Callback: HTTP响应处理阶段
    alt 请求成功
        HTTPTask->>HTTPTask: handle_http_response(client)

        alt 下载模式
            HTTPTask->>FileSystem: ADP_FS_fopen(down_file_path, mode)
            FileSystem-->>HTTPTask: 返回文件句柄
        end

        HTTPTask->>MsgQueue: sAPI_MsgQRecv(http_msgq) 等待初始响应
        MsgQueue-->>HTTPTask: 返回响应状态码和内容长度

        HTTPTask->>HTTPAPI: sAPI_HttpHead() 获取响应头
        HTTPTask->>MsgQueue: sAPI_MsgQRecv(http_msgq) 等待响应头数据
        MsgQueue-->>HTTPTask: 返回响应头数据
        HTTPTask->>Callback: 回调HTTP_CB_EVENT_RSP_HEADER事件

        loop 响应体分块处理
            HTTPTask->>HTTPAPI: http_get_body(remaining_len)
            HTTPAPI->>HTTPAPI: sAPI_HttpRead(1, 0, chunk_size)
            HTTPTask->>MsgQueue: sAPI_MsgQRecv(http_msgq) 等待响应体数据
            MsgQueue-->>HTTPTask: 返回响应体数据块

            alt 下载模式
                HTTPTask->>FileSystem: ADP_FS_fwrite(data, len, fd)
                FileSystem-->>HTTPTask: 返回写入结果
                HTTPTask->>Callback: 回调HTTP_CB_EVENT_DOWNLOAD_PROGRESS事件
            else 普通模式
                HTTPTask->>Callback: 回调HTTP_CB_EVENT_RSP_CONTENT事件
            end
        end

        alt 下载完成
            HTTPTask->>FileSystem: ADP_FS_fclose(download_fd)
            HTTPTask->>Callback: 回调HTTP_CB_EVENT_DOWNLOAD_END事件
        else 响应完成
            HTTPTask->>Callback: 回调HTTP_CB_EVENT_RSP_END事件
        end

    else 请求失败
        HTTPTask->>Callback: 回调HTTP_CB_EVENT_ERROR事件
    end

    Note over UserApp,Callback: 资源清理阶段
    HTTPTask->>HTTPTask: http_term(client)
    HTTPTask->>HTTPAPI: sAPI_HttpTerm()
    alt 文件句柄存在
        HTTPTask->>FileSystem: ADP_FS_fclose(download_fd)
    end
    HTTPTask->>HTTPClient: SAFE_FREE(client) 释放客户端内存

    HTTPTask->>MsgQueue: 继续等待下一个客户端请求
```

### 10.2 标准消息等待机制说明

#### 10.2.1 消息队列阻塞等待
- **sAPI_MsgQRecv(client_msgq, &msg, SC_SUSPEND)**：HTTP任务线程阻塞等待客户端请求消息
- **sAPI_MsgQRecv(http_msgq, &msg, SC_SUSPEND)**：等待HTTP API响应消息
- 所有消息接收都采用SC_SUSPEND模式，确保同步处理

#### 10.2.2 响应数据分块等待
- 初始响应等待：获取HTTP状态码和内容长度
- 响应头等待：获取HTTP响应头信息
- 响应体分块等待：循环等待每个数据块（最大4KB）
- 每次数据到达都会触发相应的回调事件

#### 10.2.3 文件操作等待
- 文件打开操作：同步等待文件系统响应
- 文件写入操作：同步写入数据块
- 文件关闭操作：确保数据完整性

## 11. 关键技术特点

### 11.1 异步处理架构
- 基于消息队列的生产者-消费者模式
- 单任务处理，避免并发冲突和内存泄漏
- 非阻塞的请求提交，阻塞的响应处理

### 11.2 分块数据传输
- 支持大文件的分块传输（4KB块大小）
- 内存使用优化，避免大内存分配
- 实时进度回调，用户可监控传输状态

### 11.3 完善的错误处理
- 参数验证：URL、超时时间、SSL配置等
- 资源管理：内存、文件句柄、任务线程
- 异常恢复：失败时自动清理资源

### 11.4 SSL/TLS安全支持
- 多级SSL验证：无验证、服务器验证、双向验证
- 证书管理：支持文件路径和字符串两种方式
- SSL上下文管理：支持0-9共10个SSL上下文

## 12. 使用示例

### 12.1 基本HTTP GET请求
```c
// 1. 初始化HTTP模块
ADP_HTTP_Init();

// 2. 创建HTTP客户端
HTTP_CLIENT_T *client = ADP_HTTP_Create();

// 3. 设置请求配置
HTTP_CLI_CONFIG_T config = {
    .url = "http://example.com/api/data",
    .request_type = HTTP_CLIENT_REQUEST_GET,
    .connect_timeout = 30,
    .response_timeout = 60
};
ADP_HTTP_SetCliCfg(client, &config);

// 4. 设置回调函数
HTTP_CALLBACK_T callback = {
    .http_callback = my_http_callback
};

// 5. 发起请求
ADP_HTTP_Request(client, callback);
```

### 12.2 HTTPS文件下载
```c
// 1. 创建客户端并设置基本配置
HTTP_CLIENT_T *client = ADP_HTTP_Create();
HTTP_CLI_CONFIG_T config = {
    .url = "https://example.com/files/data.zip",
    .request_type = HTTP_CLIENT_REQUEST_GET,
    .down_file_path = "C:/downloads/data.zip",
    .connect_timeout = 30,
    .response_timeout = 300
};
ADP_HTTP_SetCliCfg(client, &config);

// 2. 设置SSL配置
HTTP_SSL_CONFIG_T ssl_config = {
    .verify_level = HTTP_SSL_VERIFY_SERVER,
    .ssl_contxtid = 0,
    .root_crt_path = "C:/certs/ca.crt"
};
ADP_HTTP_SetSSLCfg(client, &ssl_config);

// 3. 发起下载请求
HTTP_CALLBACK_T callback = {
    .http_callback = download_callback
};
ADP_HTTP_Request(client, callback);
```

### 12.3 POST数据上传
```c
// 1. 准备POST数据
const char *post_data = "{\"name\":\"test\",\"value\":123}";
HTTP_CLI_CONFIG_T config = {
    .url = "https://api.example.com/data",
    .request_type = HTTP_CLIENT_REQUEST_POST,
    .headers = "Content-Type: application/json\r\n",
    .headers_len = strlen("Content-Type: application/json\r\n"),
    .postdata = post_data,
    .postdata_len = strlen(post_data),
    .connect_timeout = 30,
    .response_timeout = 60
};

// 2. 发起POST请求
HTTP_CLIENT_T *client = ADP_HTTP_Create();
ADP_HTTP_SetCliCfg(client, &config);
ADP_HTTP_Request(client, callback);
```

## 13. 注意事项与限制

### 13.1 使用限制
- 单任务处理模式，同时只能处理一个HTTP请求
- SSL上下文ID范围限制为0-9
- 最大数据包大小限制为4KB
- 消息队列深度限制为8

### 13.2 内存管理注意事项
- HTTP_CLIENT_T实例由模块自动管理，用户无需手动释放
- 配置参数中的字符串指针需要在请求完成前保持有效
- 下载文件路径字符串需要在整个下载过程中保持有效

### 13.3 线程安全
- 模块内部使用消息队列保证线程安全
- 用户回调函数在HTTP任务线程中执行
- 回调函数中不应执行耗时操作

### 13.4 错误处理建议
- 始终检查API返回值
- 在回调函数中处理HTTP_CB_EVENT_ERROR事件
- 合理设置超时时间，避免长时间阻塞

---

## 14. 版本信息

- **文件名**：adp_http.c
- **版权所有**：(c) 2024-2030 厦门雅迅智联科技股份有限公司
- **创建日期**：2025/03/25
- **创建作者**：梅超
- **文档版本**：1.0
- **最后更新**：2025/08/16

---

*本文档详细描述了adp_http.c模块的设计架构、工作流程和使用方法。如需更多技术细节或使用支持，请联系开发团队。*
