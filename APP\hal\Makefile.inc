
#include path

INCLUDES    += -I./source/hal/h
INCLUDES    += -I./source/adp/h
INCLUDES    += -I./source/shell
INCLUDES    += -I./source/shell/h
INCLUDES    += -I./source/plat/h

OPENSDK_PATH := ../../osdrv/firmware/$(firmware_plat_name)
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/inc
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/inc/GPIO
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/inc/mbedtls
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/inc/token
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/inc/utils
INCLUDES    += -I$(OPENSDK_PATH)/sc_lib/$(platform_cust_name)/inc


# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES     += source/adp/c/fk_application.c \
               source/adp/c/adp_crypto.c     \
               source/adp/c/adp_debug.c      \
               source/adp/c/adp_download.c   \
               source/adp/c/adp_fs.c         \
               source/adp/c/adp_ftp.c        \
               source/adp/c/adp_gpio.c       \
               source/adp/c/adp_gprs.c       \
               source/adp/c/adp_gps.c        \
               source/adp/c/adp_http.c       \
               source/adp/c/adp_i2c.c        \
               source/adp/c/adp_lbs.c        \
               source/adp/c/adp_misc.c       \
               source/adp/c/adp_mqtt.c       \
               source/adp/c/adp_network.c    \
               source/adp/c/adp_plat.c       \
               source/adp/c/adp_roundbuf.c   \
               source/adp/c/adp_rtos.c       \
               source/adp/c/adp_sif.c        \
               source/adp/c/adp_socket.c     \
               source/adp/c/adp_timer.c      \
               source/adp/c/adp_tools.c      \
               source/adp/c/adp_tsk.c        \
               source/adp/c/adp_uart.c       \
               source/adp/c/adp_url.c        \
               source/adp/c/adp_vm.c         \
               source/adp/c/adp_wifi.c       \
               source/adp/c/adp_main.c

SOURCES     += source/hal/c/hal_app.c      \
               source/hal/c/hal_asyntask.c \
               source/hal/c/hal_crypto.c   \
               source/hal/c/hal_download.c \
               source/hal/c/hal_fs.c       \
               source/hal/c/hal_ftp.c      \
               source/hal/c/hal_gpio.c     \
               source/hal/c/hal_gprs.c     \
               source/hal/c/hal_gps.c      \
               source/hal/c/hal_http.c     \
               source/hal/c/hal_i2c.c      \
               source/hal/c/hal_lbs.c      \
               source/hal/c/hal_mqtt.c     \
               source/hal/c/hal_network.c  \
               source/hal/c/hal_plat.c     \
               source/hal/c/hal_sif.c      \
               source/hal/c/hal_socket.c   \
               source/hal/c/hal_timer.c    \
               source/hal/c/hal_tsk.c      \
               source/hal/c/hal_uart.c     \
               source/hal/c/hal_url.c	   \
               source/hal/c/hal_mancls.c   \
               source/hal/c/hal_wifi.c     \
               source/hal/c/hal_loader.c


# sub directories, sub directory will then add to this
SUBDIRS		+= ./source/adp/c
SUBDIRS		+= ./source/hal/c
