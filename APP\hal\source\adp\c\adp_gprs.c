/********************************************************************************
**
** 文件名:     adp_gprs.c
** 版权所有:   (c) 2024-2030 厦门雅迅智联科技股份有限公司
** 文件描述:   实现GPRS上下文管理
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2024/08/31 | 叶德焰 |  创建该文件
*********************************************************************************/
#include "adp_include.h"


#if DEBUG_ADP > 0
#define ADP_LOGD(fmt, ...)  PLT_TRACE("[adp_gprs] "fmt,  ##__VA_ARGS__)
#define ADP_LOGE(fmt, ...)  PLT_TRACE("[adp_gprs] ""%d "fmt, __LINE__,  ##__VA_ARGS__)
#define ADP_LOGP(fmt, ...)  PLT_TRACE("[adp_gprs] "fmt,  ##__VA_ARGS__)
#else

#define ADP_LOGD(fmt, ...)
#define ADP_LOGE(fmt, ...)  PLT_TRACE("[adp_gprs] ""%d "fmt, __LINE__,  ##__VA_ARGS__)
#define ADP_LOGP(fmt, ...)  PLT_TRACE("[adp_gprs] "fmt,  ##__VA_ARGS__)
#endif


/*
********************************************************************************
* 定义模块配置参数
********************************************************************************
*/
#define MAX_USER             6


/*
********************************************************************************
* 定义模块数据结构
********************************************************************************
*/
typedef struct {
    char    domainname[50];
    INT8U   num;
    INT8U   contxtid;
    INT32U  ip[10];
} DNS_INFO_T;

typedef struct {
    INT8U pdpstatus[PDP_COM_MAX];
    char  apn[PDP_COM_MAX][41];
    char  username[PDP_COM_MAX][41];
    char  password[PDP_COM_MAX][41];
    char  localip[PDP_COM_MAX][16];

    ADP_GPRS_CALLBACK_T callback;
    void (*callback_getipbyname[PDP_COM_MAX])(INT8U contxtid, BOOLEAN result, INT32S error, INT8U num, INT32U *ip);
} GCB_T;


/*
********************************************************************************
* 定义模块变量
********************************************************************************
*/
static GCB_T s_dcb = {0};


/*******************************************************************
** 函数名称: ADP_GPRS_RegEvent
** 函数描述: 注册GPRS事件
** 参数: [in]  contxtid: GPRS上下文通道编号0－1，见PDP_COM_E
**       [in]  profileid:参数编号0－9
**	     [in]  callback: 注册回调函数参数
** 返回: 成功或失败
********************************************************************/
BOOLEAN ADP_GPRS_RegEvent(INT8U contxtid, INT8U profileid, ADP_GPRS_CALLBACK_T *callback)
{
    ADP_MEMCPY(&s_dcb.callback, callback, sizeof(s_dcb.callback));
    return true;
}

/*******************************************************************
** 函数名称: ADP_GPRS_SetApn
** 函数描述: 设置apn激活GPRS上下文
** 参数: [in] contxtid: GPRS上下文通道编号0－1, 见PDP_COM_E
**	     [in] apn: APN
**	     [in] username: 用户名
**	     [in] password: 密码
**	     [in] callback: 设置结果回调
** 返回: 0:成功返回，其他值见SHELL_GPRS_RESULT_E
********************************************************************/
INT32S ADP_GPRS_SetApn(INT8U contxtid, char *apn, char *username, char *password, void (*callback)(INT8U result, INT32S error_codee, void *pdata))
{
    if (contxtid >= PDP_COM_MAX || apn == PNULL) {
        return SHELL_GPRS_PARAM_ERROR;
    }

    if (ADP_STRLEN(apn) >= sizeof(s_dcb.apn[contxtid])) {
        ADP_LOGE("apn[%d]:%s over\n", contxtid, apn);
        return SHELL_GPRS_PARAM_ERROR;
    }
    ADP_MEMSET(s_dcb.apn[contxtid], 0, sizeof(s_dcb.apn[contxtid]));
    ADP_STRCPY(s_dcb.apn[contxtid], apn);

    if (username != PNULL) {
        if (ADP_STRLEN(username) >= sizeof(s_dcb.username[contxtid])) {
            ADP_LOGE("user[%d]:%s over\n", contxtid, username);
            return SHELL_GPRS_PARAM_ERROR;
        }
        ADP_MEMSET(s_dcb.username[contxtid], 0, sizeof(s_dcb.username[contxtid]));
        ADP_STRCPY(s_dcb.username[contxtid], username);
    }

    if (password != PNULL) {
        if (ADP_STRLEN(password) >= sizeof(s_dcb.password[contxtid])) {
            ADP_LOGE("pass[%d]:%s over\n", contxtid, password);
            return SHELL_GPRS_PARAM_ERROR;
        }
        ADP_MEMSET(s_dcb.password[contxtid], 0, sizeof(s_dcb.password[contxtid]));
        ADP_STRCPY(s_dcb.password[contxtid], password);
    }

    /* 返回0应用认为非阻塞成功,返回1表示需等回调函数指示结果 */
    return 0;
}

/*******************************************************************
** 函数名称: ADP_GPRS_GetApn
** 函数描述: 获取APN
** 参数: [in] contxtid: GPRS上下文通道编号0－1, 见PDP_COM_E
**	     [in] callback: 获取结果回调
** 返回: 0表示操作成功；其它见SHELL_GPRS_RESULT_E
********************************************************************/
INT32S ADP_GPRS_GetApn(INT8U contxtid, void (*callback)(INT8U id, INT8U result, INT32S error_code, INT8U *apn, INT8U *username, INT8U *password))
{
    if (contxtid >= PDP_COM_MAX || callback == PNULL) {
        return SHELL_GPRS_PARAM_ERROR;
    }

    callback(contxtid, true, 0, (INT8U *)s_dcb.apn[contxtid], (INT8U *)s_dcb.username[contxtid], (INT8U *)s_dcb.username[contxtid]);
    return 0;
}

/*******************************************************************
** 函数名称: gprs_active_process
** 函数描述: 拨号任务
** 参数: [in] argv: 私有参数
** 返回: 无
********************************************************************/
static void gprs_active_process(void * argv)
{
    INT8U contxtid = (INT8U)(INT32U)argv;
    SCcgpaddrParm info = {0};
    INT32S result = 0;

    ADP_LOGD("active entry\n");//sAPI_NetworkGetCpsi
    result = sAPI_NetworkSetCgdcont(contxtid + 1, "ip", s_dcb.apn[contxtid]);
    if (result == SC_NET_SUCCESS) {
        result = sAPI_NetworkSetCgact(1, contxtid + 1);
        //result = sAPI_TcpipPdpActive(contxtid + 1, 1);
    }
    sAPI_NetworkGetCgpaddr(contxtid + 1, &info);
    ADP_LOGP("active: cid=%d apn=%s addr:%s result=%x\n", contxtid, s_dcb.apn[contxtid], info.ipv4addr, result);

    /* 回调函数只由hal层调用,hal层已经经过消息转换所以不需要考虑资源互斥，直接调用回调函数 */
    if (result == SC_NET_SUCCESS) {
        if (s_dcb.callback.callback_network_actived != 0) {
            s_dcb.callback.callback_network_actived(contxtid);
        }
    } else {
       if (s_dcb.callback.callback_network_deactived != PNULL) {
           s_dcb.callback.callback_network_deactived(contxtid, 0, result);
       }
    }

    /* 释放本任务 */
    ADP_RTOS_TaskDelete(PNULL);
}

/*******************************************************************
** 函数名称: ADP_GPRS_ActivePDPContext
** 函数描述: 激活GPRS上下文
** 参数: [in] contxtid: GPRS上下文通道编号0－1, 见PDP_COM_E
** 返回: 0表示操作成功；其它见SHELL_GPRS_RESULT_E
********************************************************************/
INT32S ADP_GPRS_ActivePDPContext(INT8U contxtid)
{
    INT32S result = 0;
    ADP_RTOS_TASK_T qtask = NULL;

    if (contxtid >= PDP_COM_MAX) {
        return SHELL_GPRS_PARAM_ERROR;
    }
    s_dcb.callback_getipbyname[contxtid] = PNULL;

    result = ADP_RTOS_TaskCreate(&qtask, 4096, 100, "yx_active", gprs_active_process, (void *)(INT32U)contxtid);
    ADP_LOGD("ActivePDPContext: cid=%d create=%x\r\n", contxtid, result);

    if (!result) {
        s_dcb.pdpstatus[contxtid] = PDP_STATE_ACTIVING;
        return SHELL_GPRS_BOLCKING;
    } else {
        return SHELL_GPRS_CREATE_FAIL;
    }
}

/*******************************************************************
** 函数名称: gprs_deactive_process
** 函数描述: 拨号任务
** 参数: [in]  argv: 私有参数
** 返回: 无
********************************************************************/
static void gprs_deactive_process(void * argv)
{
    INT8U contxtid = (INT8U)(INT32U)argv;
    INT32S result = 0;

    result = sAPI_NetworkSetCgact(0, contxtid + 1);
    //result = sAPI_TcpipPdpDeactive(contxtid + 1, 0);
    ADP_LOGP("deactive: cid=%d result=%x\r\n", contxtid, result);

    /* 回调函数只由hal层调用,hal层已经经过消息转换所以不需要考虑资源互斥，直接调用回调函数 */
    if (s_dcb.callback.callback_network_deactived != PNULL) {
        s_dcb.callback.callback_network_deactived(contxtid, (result == SC_NET_SUCCESS) ? (1) : (0), result);
    }

    /* 释放本任务 */
    ADP_RTOS_TaskDelete(PNULL);
}

/*******************************************************************
** 函数名称: ADP_GPRS_DeactivePDPContext
** 函数描述: 去活GPRS上下文
** 参数: [in]  contxtid: GPRS上下文通道编号0－1, 见PDP_COM_E
** 返回: 0表示操作成功；其它见SHELL_GPRS_RESULT_E
********************************************************************/
INT32S ADP_GPRS_DeactivePDPContext(INT8U contxtid)
{
    INT32S result = 0;
    ADP_RTOS_TASK_T qtask = NULL;

    if (contxtid >= PDP_COM_MAX) {
        return SHELL_GPRS_PARAM_ERROR;
    }
    s_dcb.callback_getipbyname[contxtid] = PNULL;

    result = ADP_RTOS_TaskCreate(&qtask, 4096, 100, "yx_deactive", gprs_deactive_process, (void *)(INT32U)contxtid);
    ADP_LOGD("DeactivePDPContext: cid=%d create=%x\r\n", contxtid, result);

    if (!result) {
        s_dcb.pdpstatus[contxtid] = PDP_STATE_DEACTIVING;
        return SHELL_GPRS_BOLCKING;
    } else {
        return SHELL_GPRS_CREATE_FAIL;
    }
}

/*******************************************************************
** 函数名称: ADP_GPRS_GprsNetworkGetState
** 函数描述: 获取GPRS上下文和GPRS网络状态
** 参数: [in]  contxtid: GPRS上下文通道编号0－1
**       [out] pdpstate: GPRS上下文状态，见PDP_STATE_E
**       [out] gprsreg： gprs注册状态: GPRS上下文状态，1表示GPRS已注册，其他表示未注册
** 返回: 0表示操作成功；其它见SHELL_GPRS_RESULT_E
********************************************************************/
INT32S ADP_GPRS_GprsNetworkGetState(INT8U contxtid, INT32U *pdpstate, INT8U *gprsreg)
{
    if (contxtid >= PDP_COM_MAX || pdpstate == PNULL || gprsreg == PNULL) {
        return SHELL_GPRS_PARAM_ERROR;
    }

    *pdpstate = s_dcb.pdpstatus[contxtid];
    *gprsreg = 0;
    return 0;
}

/*******************************************************************
** 函数名:     ADP_GPRS_GetLocalIpAddr
** 函数描述:   获得本地IP地址
** 参数:       [in]  contxtid: GPRS上下文通道编号0－1
**             [out] ipaddr:   ip地址,依次从从高位到低位
** 返回:       成功返回0，失败返回-1
********************************************************************/
INT32S ADP_GPRS_GetLocalIpAddr(INT8U contxtid, INT32U *ipaddr)
{
    *ipaddr = 0;
    return 0;
}

/*******************************************************************
** 函数名:     ADP_GPRS_GetIpByDomainName_Handler
** 函数描述:   域名解析异步消息处理函数
** 参数:       [in]  domainname: 域名，如“www.yaxon.com”
**           [in]  fp:         异步回调函数
** 返回:       成功返回true，失败返回false
********************************************************************/
void ADP_GPRS_GetIpByDomainName_Handler(INT32U tskid, INT32U wpara, INT32U dwpara)
{
    DNS_INFO_T *info = (DNS_INFO_T *)dwpara;

    if (info == PNULL) {
        return ;
    }

    if (info->contxtid >= PDP_COM_MAX) {
        ADP_FREE(info);
        return;
    }

    if (s_dcb.callback_getipbyname[info->contxtid] == PNULL) {
        ADP_FREE(info);
        return;
    }

    if (wpara == TRUE) {
        s_dcb.callback_getipbyname[info->contxtid](info->contxtid, true, 0, info->num, info->ip);
    } else {
        s_dcb.callback_getipbyname[info->contxtid](info->contxtid, FALSE, 0, info->num, info->ip);
    }

    s_dcb.callback_getipbyname[info->contxtid] = PNULL;
    ADP_FREE(info);
}

/*******************************************************************
** 函数名:     gprs_get_domain_name_process
** 函数描述:  域名解析任务
** 参数:       [in]  argv: 私有参数
** 返回:       0表示操作成功；
********************************************************************/
static void gprs_get_domain_name_process(void * argv)
{
    DNS_INFO_T *info = (DNS_INFO_T *)argv;
    struct sockaddr_in	* ip4_svr_addr;
    struct addrinfo		* res = PNULL, *res_next = PNULL,  hints;

    if (info == PNULL) {
        return;
    }

    hints.ai_family = AF_INET;
	hints.ai_socktype = SOCK_STREAM;

    if (getaddrinfo_with_pcid(info->domainname, NULL, &hints, &res, info->contxtid + 1) == 0) {

        res_next = res;
        info->num = 0;
        while (res_next != PNULL && info->num  < 10) {
            ip4_svr_addr = (struct sockaddr_in *)res_next->ai_addr;
            info->ip[info->num] = htonl(ip4_svr_addr->sin_addr.s_addr);
            
            info->num++;
            res_next = res_next->ai_next;
        }

        if (info->num == 0) {
            ADP_TSK_PostEvent(SHELL_MAIN_TSK, ADP_EVENT_ATTRIB_ASYNC, YX_GPS_EVT_DOMAINNAME, FALSE, (INT32U)info);
        } else {
            ADP_TSK_PostEvent(SHELL_MAIN_TSK, ADP_EVENT_ATTRIB_ASYNC, YX_GPS_EVT_DOMAINNAME, TRUE, (INT32U)info);
        }
        
    } else {
        ADP_TSK_PostEvent(SHELL_MAIN_TSK, ADP_EVENT_ATTRIB_ASYNC, YX_GPS_EVT_DOMAINNAME, FALSE, (INT32U)info);
    }

    if (res != PNULL) {
        freeaddrinfo(res);
    }
    
    /* 释放本任务 */
    ADP_RTOS_TaskDelete(PNULL);
}

/*******************************************************************
** 函数名:     ADP_GPRS_GetIpByDomainName
** 函数描述:   根据域名获取IP地址
** 参数:       [in]  domainname: 域名，如“www.yaxon.com”
**           [in]  fp:         异步回调函数
** 返回:       成功返回true，失败返回false
********************************************************************/
BOOLEAN ADP_GPRS_GetIpByDomainName(INT8U contxtid, char *domainname, void (*fp)(INT8U contxtid, BOOLEAN result, INT32S error, INT8U num, INT32U *ip))
{
    INT32S result;
    ADP_RTOS_TASK_T qtask;
    DNS_INFO_T *info;

    if (contxtid >= PDP_COM_MAX || domainname == PNULL) {
        return FALSE;
    }

    if (s_dcb.callback_getipbyname[contxtid] != PNULL) {
        return FALSE;
    }

    info = (DNS_INFO_T *)ADP_ALLOC(sizeof(DNS_INFO_T));
    if (info == NULL) {
        return FALSE;
    }
    ADP_MEMSET(info, 0, sizeof(DNS_INFO_T));

    if (ADP_STRLEN(domainname) >= sizeof(info->domainname)) {
        ADP_FREE(info);
        return FALSE;
    }

    s_dcb.callback_getipbyname[contxtid] = fp;
    ADP_STRCPY(info->domainname, domainname);
    info->contxtid = contxtid;
    info->num = 0;

    result = ADP_RTOS_TaskCreate(&qtask, 2048, 100, "yx_dns", gprs_get_domain_name_process, (void *)info);
    if (result == 0) {
        return TRUE;
    }

    if (fp != PNULL) {
        fp(contxtid, false, SHELL_GPRS_CREATE_FAIL, 0, PNULL);
        s_dcb.callback_getipbyname[contxtid] = PNULL;
    }
    ADP_LOGE("create fail:%x\n", result);
    ADP_FREE(info);
    return FALSE;
}

/*******************************************************************
** 函数名:     ADP_GPRS_SetDnsAddress
** 函数描述:   设置域名服务器地址
** 参数:       [in]  contxtid:   GPRS上下文通道编号0－1
**             [in]  num:        ip个数,一般为2个
**             [in]  ip:         ip地址,依次从从高位到低位
** 返回:       成功返回true，失败返回false
********************************************************************/
BOOLEAN ADP_GPRS_SetDnsAddress(INT8U contxtid, INT8U num, INT32U *ip)
{
    return false;
}

/*******************************************************************
** 函数名:     ADP_GPRS_GetDnsAddress
** 函数描述:   获取域名服务器地址
** 参数:       [in]  contxtid:   GPRS上下文通道编号0－1
**             [in]  i_num:      ip缓存个数
**             [out] ip:         ip缓存,依次从从高位到低位
**             [out] o_num:      返回ip个数
** 返回:       成功返回true，失败返回false
********************************************************************/
BOOLEAN ADP_GPRS_GetDnsAddress(INT8U contxtid, INT8U i_num, INT32U *ip, INT8U *o_num)
{
    return false;
}

/*******************************************************************
** 函数名:     ADP_GPRS_Init
** 函数描述:   模块初始化函数
** 参数:       无
** 返回:       无
********************************************************************/
void ADP_GPRS_Init(void)
{
    ADP_MEMSET(&s_dcb, 0, sizeof(GCB_T));
}

/*******************************************************************
** 函数名:     ADP_GPRS_Deinit
** 函数描述:   模块去初始化函数
** 参数:       无
** 返回:       无
********************************************************************/
void ADP_GPRS_Deinit(void)
{
    ADP_MEMSET(&s_dcb, 0, sizeof(GCB_T));
}
